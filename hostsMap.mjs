// Couples (referer (with the final slash), name-of-the-secret)
export const HOSTSMAP = new Map([

    // STAGE
    ['http://localhost:3000/', 'tamtamy-stage'],
    ['http://localhost:8000/', 'chicknorristv-stage'],
    ['https://api-videoplatform-stage.reply.com/', 'serverless'],
    ['https://dam-stage-conf.reply.com/', 'discoverydam'],
    ['https://vp-player-stage.reply.com/', 'tamtamy-stage'],
    ['https://videoplatform-demo.reply.com/',  'videoplatform-demo-stage'],
    ['https://sviltamtamy.reply.com/', 'tamtamy-stage'],


    // ROSE
    ['https://d3c431922ei0up.cloudfront.net/', 'rose-stage'],
    ['https://dam-widget-stage.reply.com/', 'rose-stage'],
    ['https://devonlineservices.reply.com/', 'rose-stage'],
    ['https://testonlineservices.reply.com/', 'rose-stage'],
    ['https://preonlineservices.reply.com/', 'rose-stage'],
    ['https://api-devonlineservices.reply.com/', 'rose-stage'],
    ['https://api-testonlineservices.reply.com/', 'rose-stage'],
    ['https://api-preonlineservices.reply.com/', 'rose-stage'],

    ['https://onlineservices.reply.com/', 'rose-prod'],
    ['https://api-onlineservices.reply.com/', 'rose-prod'],
    ['https://dcs7t2sdqmsaq.cloudfront.net/', 'rose-prod'],
    ['https://dam-widget.reply.com/', 'rose-prod'],



    // WEBINARS
    ['https://webinars-authorizer-test.reply.com/', 'webinars-stage'],
    ['https://webinars-test.reply.com/', 'webinars-stage'],

    ['https://webinars.reply.com/', 'webinars-prod'],
    ['https://webinars-authorizer.reply.com/', 'webinars-prod'],



    //TENANT
    ['https://chicknorristv-stage.reply.com/', 'chicknorristv-stage'],
    ['https://xchangestage.reply.com/',  'xchange-stage'],
    ['https://cntvambassadors-stage.reply.com/',  'cntvambassadors-stage'],
    ['https://woodmeeting-stage.reply.com/', 'woodmeeting-stage'],
    ['https://speeddating-stage.reply.com/', 'speeddating-stage'],


    // IMPORTANT: env variables are not supported by Lambda@Edge, adjust the code accordingly to the deployment environment
    // Digital signage - PROD
    ["http://localhost:80/", "serverless-prod"],
    ["http://localhost/", "serverless-prod"],

    // Digital signage - STAGE
    //["http://localhost:80/", "serverless"],
    //["http://localhost/", "serverless"],



    // PROD
    ['https://api-videoplatform.reply.com/', 'serverless-prod'],
    ['https://dam-conf.reply.com/', 'discoverydam'],
    ['https://vp-player.reply.com/', 'tamtamy-prod'],
    ['https://tamtamy.reply.com/', 'tamtamy-prod'],

    // ['https://sviltamtamy.reply.com/', 'tamtamy-stage'],
    // TENANT
    ['https://chicknorristv.reply.com/', 'chicknorristv-prod'],
    ['https://xchange.reply.com/',  'xchange-prod'],
    ['https://xchangepress.reply.com/', 'xchangepress-prod'],
    ['https://xchangestreaming.reply.com/', 'xchangestreaming-prod'],
    ['https://cntvambassadors.reply.com/',  'cntvambassadors-prod'],
    ['https://woodmeeting.reply.com/', 'woodmeeting-prod'],
    ['https://speeddating.reply.com/', 'speeddating-prod'],
    ['https://hyperspace.reply.com/', 'discoverydam'],
    ['https://techtv.reply.com/', 'techtv-prod'],
    ['https://bluetv.reply.com/', 'bluetv-prod'],
    ['https://clustertv.reply.com/', 'clustertv-prod'],
    ['https://spiketv.reply.com/', 'spiketv-prod'],



    // PROD TEMP
    ['https://vp-player-prod.reply.com/', 'tamtamy-prod'],
    ['https://chicknorristv-conf.reply.com/', 'chicknorristv-prod'],
    ['https://xchange-conf.reply.com/', 'xchange-prod'],
    ['https://xchange22-conf.reply.com/', 'xchange-prod'],
    ['https://xchangepress-conf.reply.com/', 'xchangepress-prod'],
    ['https://xchangestreaming-conf.reply.com/', 'xchangestreaming-prod'],
    ['https://cntvambassadors-conf.reply.com/', 'cntvambassadors-prod'],
    ['https://woodmeeting-conf.reply.com/', 'woodmeeting-prod'],
    ['https://speeddating-conf.reply.com/', 'speeddating-prod'],
    ['https://techtv-conf.reply.com/', 'techtv-prod'],
    ['https://bluetv-conf.reply.com/', 'bluetv-prod'],
    ['https://clustertv-conf.reply.com/', 'clustertv-prod'],
    ['https://spiketv-conf.reply.com/', 'spiketv-prod'],

    // PREPROD
    ['https://woodmeeting-preprod.reply.com/', 'woodmeeting-prod'],
    ['https://chicknorris-preprod.reply.com/', 'chicknorristv-prod'],
    ['https://xchange-preprod.reply.com/', 'xchange-prod'],
    ['https://cntvambassadors-preprod.reply.com/', 'cntvambassadors-prod'],

]);
