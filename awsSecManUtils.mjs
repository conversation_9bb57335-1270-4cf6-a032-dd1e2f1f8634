import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";

const secretsManager = new SecretsManagerClient({
  region: 'eu-west-1', 
});


/**
 * Retrieves the INITVEC and PRIVATEKEY from AWS Secrets Manager based on the host.
 *
 * @param {string} host - The host header value.
 * @returns {Promise<{INITVEC: string, PRIVATEKEY: string}>} - An object containing the IV and private key.
 */
export async function getSecretsForHost(host) {
  const secretName = `jwtLambdaAuthSec-host-${host}`;
  
  //console.log("secretName:" , secretName)

  try {
    const response = await secretsManager.send(
      new GetSecretValueCommand({
        SecretId: secretName,
        VersionStage: "AWSCURRENT", // VersionStage defaults to AWSCURRENT if unspecified
      })
    );

    let secretValues;
    if (response.SecretString) {
      // Parse the secret string to JSON
      secretValues = JSON.parse(response.SecretString);
    } else {
      // If the secret is a binary type
      const buff = Buffer.from(response.SecretBinary, 'base64');
      secretValues = JSON.parse(buff.toString('ascii'));
    }

    const { INITVEC, PRIVATEKEY, JWTKEY } = secretValues;

    if (!INITVEC || !PRIVATEKEY || !JWTKEY) {
      throw new Error('INITVEC or PRIVATEKEY or JWTKEY is missing in the secret');
    }

    return { INITVEC, PRIVATEKEY, JWTKEY };

  } catch (err) {
    console.error(`Error retrieving secrets for host ${host}:`, err);
    throw err;
  }
}
