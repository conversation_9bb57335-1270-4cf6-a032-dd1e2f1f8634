export function getCookieValues(cookies, cookieName) {
    if (!cookies) {
        return [];
    }

    const matchingValues = [];


    const cookiePairs = cookies.split(';').map(cookie => cookie.trim());

    for (const cookie of cookiePairs) {
        const [name, ...rest] = cookie.split('=');
        if (name && name.includes(cookieName)) {
            const value = rest.join('='); // Handle cases where value contains '='
            matchingValues.push(value);
        }

    }

    return matchingValues;
}

export function compareBasePaths(pathWithWildcard, actualPath) {
    const basePathWithWildcard = pathWithWildcard.substring(0, pathWithWildcard.lastIndexOf('/') + 1);
    const basePathActual = actualPath.substring(0, actualPath.lastIndexOf('/') + 1);

    console.log('WILDCARD ' + basePathWithWildcard);
    console.log('BASE ACTUAL: ' + basePathActual);

    return basePathWithWildcard === basePathActual;
}

/**
 * Retrieves the value of a specific query parameter from a raw query string.
 *
 * @param {string} paramName - The name of the query parameter to find.
 * @param {string} queryString - The raw query string (e.g., "key=value&foo=bar").
 * @returns {string|null} The value of the query parameter if found, otherwise null.
 */
export function getQueryParamValue(paramName, queryString) {
    if (!queryString) {
        return null;
    }

    // Split by '&' to get each key-value pair
    const pairs = queryString.split('&');

    for (const pair of pairs) {
        // Split by '=' to separate key and value
        const [key, value = ''] = pair.split('=');

        if (key === paramName) {
            return value; // Return the exact value without decoding
        }
    }

    // Parameter not found
    return null;
}

// Example usage:
// const qs = "discoveryAuth=k6d7yAaFUry5GOL1GI41zJSfoH8F+wQ+rJTkXpeLD/FkZYOaPoe0Pud...&other=param";
// const value = getQueryParamValue("discoveryAuth", qs);
// console.log(value); // prints "k6d7yAaFUry5GOL1GI41zJSfoH8F+wQ+rJTkXpeLD/FkZYOaPoe0Pud..."
