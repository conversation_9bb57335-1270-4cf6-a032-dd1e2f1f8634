import crypto from 'crypto';

import { decryptSymmetric } from './cryptoUtils.mjs';

function _base64urlDecode(str) {
    return Buffer.from(str, 'base64url').toString();
}

function _constantTimeEquals(a, b) {
    if (a.length != b.length) {
        return false;
    }

    let xor = 0;
    for (let i = 0; i < a.length; i++) {
        xor |= (a.charCodeAt(i) ^ b.charCodeAt(i));
    }
    return xor === 0;
}

function _verify(input, key, method, type, signature) {
    if (type === 'hmac') {
        return _constantTimeEquals(signature, _sign(input, key, method));
    } else {
        throw new Error('Algorithm type not recognized');
    }
}

function _sign(input, key, method) {
    return crypto.createHmac(method, key).update(input).digest('base64url');
}

export function jwt_decode(token, key, noVerify) {
    if (!token) {
        throw new Error('No token supplied');
    }

    const segments = token.split('.');
    if (segments.length !== 3) {
        throw new Error('Not enough or too many segments');
    }

    const headerSeg = segments[0];
    const payloadSeg = segments[1];
    const signatureSeg = segments[2];

    const header = JSON.parse(_base64urlDecode(headerSeg));
    const payload = JSON.parse(_base64urlDecode(payloadSeg));

    if (!noVerify) {
        const signingMethod = 'sha256';
        const signingType = 'hmac';
        const signingInput = [headerSeg, payloadSeg].join('.');

        if (!_verify(signingInput, key, signingMethod, signingType, signatureSeg)) {
            throw new Error('Signature verification failed');
        }

        if (payload.nbf && Date.now() < payload.nbf * 1000) {
            throw new Error('Token not yet active');
        }

        // **Ensure `exp` field is present and valid**
        if (payload.exp === undefined || payload.exp === null || isNaN(payload.exp)) {
            throw new Error('Token is missing the required "exp" (expiration time) field or it is invalid (NaN');
        }

        if (Date.now() > payload.exp * 1000) {
            throw new Error('Token expired');
        }

        console.log('JWT Signature verified correctly');
    }

    return payload;
}


/**
 * Decrypts and decodes a JWT using the provided keys.
 * @param {string} ciphertext - The encrypted JWT from the cookie.
 * @param {string} privateKey - The private key used for decryption.
 * @param {string} initVec - The initialization vector used for decryption.
 * @param {string} jwtKey - The key used to decode the JWT payload.
 * @returns {object|null} - Returns the decoded JWT payload if successful, or null if an error occurs.
 */
export function decryptAndDecodeJWT(ciphertext, privateKey, initVec, jwtKey) {
    try {
        const plaintext = decryptSymmetric(privateKey, ciphertext, initVec);
        const payload = jwt_decode(plaintext, jwtKey, false);
        return payload;
        
    } catch (err) {
        console.log(`Error decrypting or decoding JWT ${ciphertext}:`, err, "Continuing to next JWT if available...");
        return null;
    }
}

