import { inspect } from 'util';
import { decryptAndDecodeJWT } from './jwtUtils.mjs';
import { getCookieValues, compareBasePaths, getQueryParamValue } from './headersUtils.mjs';
import { getSecretsForHost } from './awsSecManUtils.mjs';
import { HOSTSMAP } from './hostsMap.mjs';

const DISCOVERYAUTH = 'discoveryAuth';

const response403 = {
    status: '403',
    statusDescription: 'Unauthorized: you shall not pass',
};

export const handler = async (event, context, callback) => {

    console.log(inspect(event, false, null, true));

    const request = event.Records[0].cf.request;
    const headers = request.headers;
    const headersMediaPath = request.uri;

    console.log("Authenticating request for media at path: ", headersMediaPath);

    const userAgentHeader = headers['user-agent'] && headers['user-agent'].length > 0 ? headers['user-agent'][0].value: '';

    // Check if the 'user-agent' header contains 'zencoder'
    if (userAgentHeader && userAgentHeader.toLowerCase().includes('zencoder')) {
        console.log("Authenticating request from Zencoder, skipping all checks");
        return callback(null, request);
    }

    // Extract query string parameters
    const queryString = request.querystring || '';

    // Check if discoveryAuth JWT is present in query parameters
    let jwtDiscoveryAuths = [];
    let isQueryParamUsed = false;

    let jwtParam = getQueryParamValue(DISCOVERYAUTH, queryString);
    if (jwtParam) {
        // If the query param is present and not empty, decode it
        try {
            jwtParam = decodeURIComponent(jwtParam);
            isQueryParamUsed = true;
            jwtDiscoveryAuths.push(jwtParam);
            console.log("JWT retrieved from query parameter (decoded):", jwtParam);
        } catch (e) {
            console.log('Error decoding discoveryAuth query parameter:', e);
            // If decoding fails, fallback to cookies
        }
    } else {
        console.log('No discoveryAuth query param found or it is empty, falling back to cookie...');
    }

    // If not using query param, fallback to cookie
    if (!isQueryParamUsed) {
        const headersCookies = headers.cookie && headers.cookie.length > 0 ? headers.cookie[0].value : null;
        jwtDiscoveryAuths = getCookieValues(headersCookies, DISCOVERYAUTH);

        if (!jwtDiscoveryAuths || jwtDiscoveryAuths.length === 0) {
            console.log('Error: No JWT found in cookies and no query parameter provided');
            return callback(null, response403);
        }
        console.log(`Received ${jwtDiscoveryAuths.length} JWT(s) from cookies`);
    }

    // https://xxx.com/ with the final slash
    const refererHeader = headers.referer && headers.referer.length > 0 ? headers.referer[0].value : '';
    let refererHeaderDomain = refererHeader != '' ? new URL(refererHeader).origin + '/' : '';

    // Check x-referer
    const xRefererHeader = headers['x-referer'] && headers['x-referer'].length > 0 ? headers['x-referer'][0].value : '';
    let xRefererHeaderDomain = xRefererHeader !== '' ? new URL(xRefererHeader).origin + '/' : '';

    if (xRefererHeaderDomain !== '') {
        // If x-referer is present, use it
        refererHeaderDomain = xRefererHeaderDomain;
    }

    // Handle edge cases where referer and xReferer are not defined
    if (xRefererHeaderDomain === '' && refererHeaderDomain === '' && (userAgentHeader.toLowerCase().includes('tizen') || userAgentHeader.toLowerCase().includes('smartdigitalsignage'))){
        refererHeaderDomain = "http://localhost/";
    }

    const referer = HOSTSMAP.has(refererHeaderDomain) ? HOSTSMAP.get(refererHeaderDomain) : '';

    // https://xxx.com without the final slash
    const originHeader = headers.origin && headers.origin.length > 0 ? headers.origin[0].value : '';

    // Access-Control-Allow-Origin = caller origin
    // Handles edge cases in which origin == "null", in that case the header must be null too
    request.headers['access-control-allow-origin'] = originHeader != "null" && originHeader != "" ? [{ key: 'Access-Control-Allow-Origin', value: originHeader }] : [{ key: 'Access-Control-Allow-Origin', value: "null" }];

    // Access-Control-Allow-Credentials = true
    request.headers['access-control-allow-credentials'] = [{ key: 'Access-Control-Allow-Credentials', value: "true" }];

    console.log("Retrieving secrets from Secrets Manager for referer", refererHeaderDomain);
    let INITVEC, PRIVATEKEY, JWTKEY;
    try {
        const secrets = await getSecretsForHost(referer);
        INITVEC = secrets.INITVEC;
        PRIVATEKEY = secrets.PRIVATEKEY;
        JWTKEY = secrets.JWTKEY;
    } catch (err) {
        console.log("Error retrieving secrets:", err);
        return callback(null, response403);
    }

    for (const jwtDiscoveryAuth of jwtDiscoveryAuths) {
        console.log(`Attempting to decrypt and decode JWT: ${jwtDiscoveryAuth}`);
        const payload = decryptAndDecodeJWT(jwtDiscoveryAuth, PRIVATEKEY, INITVEC, JWTKEY);

        if (payload) {
            // If the JWT payload was successfully decoded, check if it's valid for this request
            console.log("Decoded JWT Payload:", payload);

            // Check if the payload matches the requested path
            const pathToMedia = payload.mediaPath;
            const idClient = payload.channelId;

            // Handle Range field if existent
            const range = payload.headers && payload.headers['Range'] && payload.headers['Range'].length > 0
                ? payload.headers['Range']
                : null;

            if (range) {
                request.headers['range'] = [{ key: 'Range', value: range }];
            }

            // DAM Authorization
            if (pathToMedia.endsWith('*') && idClient === 'dam') {
                console.log("Wildcard from dam detected, returning request");
                return callback(null, request);
            }

            // CMS Authorization
            if (pathToMedia.endsWith('*') && idClient === 'CMS') {
                console.log("Wildcard from CMS detected, returning request");
                return callback(null, request);
            }

            // Multiple media wildcard
            if (pathToMedia.endsWith('*') && compareBasePaths(pathToMedia, headersMediaPath)) {
                console.log("Wildcard for multiple medias in that path detected, returning request");
                return callback(null, request);
            }

            if (headersMediaPath === pathToMedia) {
                // If the path in the request matches the path in the JWT payload, authorize the request
                console.log("JWT is valid and path matches, returning request");
                return callback(null, request);
            } else {
                console.log(`Warning: JWT path (${pathToMedia}) does not match the request path (${headersMediaPath}). Continuing to next JWT if available...`);
                // Not returning here because another JWT might match the correct path
            }
        }
    }

    // If we reached this point, either all JWTs were invalid or no JWT matched the request path
    console.log('Error: No valid JWT found that matches the path. Returning 403 Unauthorized.');

    return callback(null,response403);
};