import crypto from 'crypto';

/**
 * Encrypts the given plaintext using AES-256-CBC symmetric encryption.
 *
 * @param key - Encryption key. Size 64-byte, encoded in Base64.
 * @param plaintext - Plaintext to be encrypted. It is the Base64 encoded JWT.
 * @param iv - Initialization vector (IV). Size 16-byte for AES-256-CBC, encoded in Base64.
 * @returns {string} Base64 encoded ciphertext.
 */
export function encryptSymmetric(key, plaintext, iv) {
    const cipher = crypto.createCipheriv(
        'aes-256-cbc',
        Buffer.from(key, 'base64'),
        Buffer.from(iv, 'base64')
    );

    let ciphertext = cipher.update(plaintext, 'utf8', 'base64');
    ciphertext += cipher.final('base64');

    return ciphertext;
}

/**
 * Decrypts the given ciphertext using AES-256-CBC symmetric decryption.
 *
 * @param key - Decryption key. Size 64-byte, encoded in Base64.
 * @param ciphertext - The encrypted JWT.
 * @param iv - Initialization vector (IV). Size 16-byte for AES-256-CBC, encoded in Base64.
 * @returns {string} Decrypted plaintext.
 */
export function decryptSymmetric(key, ciphertext, iv) {
    const decipher = crypto.createDecipheriv(
        'aes-256-cbc',
        Buffer.from(key, 'base64'),
        Buffer.from(iv, 'base64')
    );

    let plaintext = decipher.update(ciphertext, 'base64', 'utf8');
    plaintext += decipher.final('utf8');

    return plaintext;
}
